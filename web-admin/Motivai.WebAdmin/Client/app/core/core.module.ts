import { DataStore } from './store/data.store';
import { CommonModule } from '@angular/common';
import { HttpModule } from '@angular/http';
import { AuthGuard } from './auth/auth.guard';
import { NgModule, Optional, SkipSelf } from '@angular/core';

import { SettingsService } from './settings/settings.service';
import { ApiService } from './api/api.service';
import { ThemesService } from './themes/themes.service';
import { TranslatorService } from './translator/translator.service';
import { MenuService } from './menu/menu.service';
import { AuthStore } from './auth/auth.store';
import { AuthService } from './auth/auth.service';
import { CorreiosService } from './services/correios.service';
import { UsersService } from './services/users.service';

import { throwIfAlreadyLoaded } from './module-import-guard';
import { NavigationService } from './services/navigation.service';
import { TrackerService } from './services/tracker.service';

@NgModule({
    imports: [
        CommonModule,
        HttpModule
    ],
    providers: [
        SettingsService,
        ThemesService,
        TranslatorService,
        MenuService,
        // Auths
        AuthService,
        AuthGuard,
        AuthStore,
        ApiService,
        DataStore,
        CorreiosService,
        UsersService,
        TrackerService,
        NavigationService
    ],
    declarations: [
    ],
    exports: [
    ]
})
export class CoreModule {
    constructor(@Optional() @SkipSelf() parentModule: CoreModule) {
        throwIfAlreadyLoaded(parentModule, 'CoreModule');
    }
}
