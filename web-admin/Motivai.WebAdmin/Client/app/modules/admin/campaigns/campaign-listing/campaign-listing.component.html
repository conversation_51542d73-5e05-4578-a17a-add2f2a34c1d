<div class="content-heading">
  Campanhas
  <small>Gerenciamento de campanhas cadastradas</small>
</div>

<gp-card *ngIf="canCreateCampaign">
  <button class="btn btn-default btn-lg btn-main" [routerLink]="['/campanha', 'nova']">
    Nova campanha <i class="fa fa-plus" style="margin-left: 5px"></i>
  </button>
</gp-card>

<gp-card title="Filtrar Campanhas">
  <div class="row">
    <gp-form-col cols="12 5 5 5">
      <input
        type="text"
        class="form-control"
        name="name"
        [(ngModel)]="filterParams.name"
        placeholder="Por nome da campanha"
      />
    </gp-form-col>
    <gp-form-col cols="12 5 5 5">
      <ng-select
        [allowClear]="false"
        [items]="clients"
        placeholder="Por cliente"
        (data)="onClientChange($event)"
      ></ng-select>
    </gp-form-col>
    <gp-form-col cols="12 5 5 5">
      <gp-select [items]="status" [(ngModel)]="filterParams.status"> </gp-select>
    </gp-form-col>
    <gp-form-col cols="12 2 2 2">
      <gp-spinner-button
        text="Pesquisar"
        [search]="true"
        (click)="loadCampaigns()"
        [loading]="loading"
        loadingText="Pesquisando"
      ></gp-spinner-button>
    </gp-form-col>
  </div>
</gp-card>

<gp-card title="Campanhas Cadastradas">
  <div class="row">
    <div class="col-xs-12">
      <gp-grid
        name="partnersGrid"
        [loading]="loading"
        [rows]="campaigns"
        [columns]="['Nome', 'Tipo', 'Cliente', 'Período']"
        [fields]="['name', 'typeDesc', 'clientName', 'period']"
        [showActive]="false"
        [showPagination]="false"
        [showEdit]="true"
        [showDelete]="false"
        (onEdit)="navigateToView($event)"
      >
      </gp-grid>
    </div>
  </div>
  <div class="row">
    <div class="col-md-12" style="padding-top:5px;">
      <div class="div-alert">
        <gp-alert #gpAlert></gp-alert>
      </div>
    </div>
  </div>
</gp-card>
