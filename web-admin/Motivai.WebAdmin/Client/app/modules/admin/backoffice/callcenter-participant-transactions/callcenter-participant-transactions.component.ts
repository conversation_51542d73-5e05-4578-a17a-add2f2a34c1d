import { Component, ViewChild, OnInit } from '@angular/core';
import { Router } from '@angular/router';

import { GpAlertComponent } from '../../../../shared/components/gp-alert/gp-alert/gp-alert.component';
import { CallcenterSession, CALLCENTER_STORE_KEY } from '../callcenter-session';
import { DataStore } from '../../../../core/store/data.store';
import { TransactionService } from '../transaction.service';
import { ParticipantService } from '../participant.service';
import { compareStrings } from '../../../../shared/helpers/comparators';

@Component({
  selector: 'callcenter-participant-transactions',
  templateUrl: 'callcenter-participant-transactions.component.html',
  styles: [`
    .align-right {
      text-align: right;
    }
  `]
})
export class CallcenterParticipantTransactionsComponent implements OnInit {
  @ViewChild('gpAlert') gpAlert: GpAlertComponent;

  callcenterSession: CallcenterSession;
  filters: any = {};
  loading: boolean = false;
  transactions: Array<any> = [];
  showBalance: boolean = true;
  participantBalance: number = 0;

  constructor(private _gpDataStore: DataStore, private _route: Router,
    private _participantService: ParticipantService,
    private _transactionService: TransactionService) { }

  get isEmpty() {
    return !this.transactions || !this.transactions.length;
  }

  ngOnInit() {
    this.callcenterSession = this._gpDataStore.get(CALLCENTER_STORE_KEY);
    if (!this.callcenterSession) {
      this._route.navigate(['/callcenter/pesquisa']);
    }
    this.loadParticipantBalance();
    this.getTransactions();
  }

  getTransactions() {
    this.loading = true;
    this.transactions = [];
    this._transactionService.getTransactions(this.callcenterSession.userId, this.callcenterSession.campaignId,
        this.filters.transactionOrigin, this.filters.startDate, this.filters.endDate)
      .subscribe(
        transactions => {
          if (transactions && transactions.length > 0) {
            transactions.sort((a, b) => compareStrings(a.processingDate, b.processingDate));
            this.transactions = transactions;
          }
          this.loading = false;
        },
        err => {
          this.loading = false;
          this.gpAlert.handleAndShowError(err);
        }
      );
  }

  private loadParticipantBalance() {
    this._participantService.getParticipantBalance(this.callcenterSession.userId, this.callcenterSession.campaignId)
      .subscribe(
        balance => {
          this.showBalance = true;
          this.participantBalance = balance;
        },
        err => {
          this.showBalance = false;
          this.gpAlert.handleAndShowError(err);
        }
      );
  }
}
