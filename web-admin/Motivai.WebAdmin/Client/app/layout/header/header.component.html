<!-- START Top Navbar-->
<nav class="navbar topnavbar" role="navigation">
    <!-- START navbar header-->
    <div class="navbar-header">
        <a class="navbar-brand" routerLink="/">
            <div class="brand-logo">
                <img class="img-responsive" src="/assets/img/logo-text.png" alt="App Logo" />
            </div>
            <div class="brand-logo-collapsed">
                <img class="img-responsive" src="/assets/img/logo-single.png" alt="App Logo" />
            </div>
        </a>
    </div>
    <!-- END navbar header-->
    <!-- START Nav wrapper-->
    <div class="nav-wrapper">
        <!-- START Left navbar-->
        <ul class="nav navbar-nav">
            <li>
                <!-- Button used to collapse the left sidebar. Only visible on tablet and desktops-->
                <a class="hidden-xs" trigger-resize="" (click)="toggleCollapsedSideabar()" *ngIf="!isCollapsedText() && !noSideBar">
                    <em class="fa fa-navicon"></em>
                </a>
                <!-- Button to show/hide the sidebar on mobile. Visible on mobile only.-->
                <a class="visible-xs sidebar-toggle" (click)="settings.layout.asideToggled =! settings.layout.asideToggled">
                    <em class="fa fa-navicon"></em>
                </a>
            </li>
            <li>
                <a title="Sair" (click)="logoff($event)"><em class="icon-logout"></em></a>
            </li>
        </ul>
        <!-- END Left navbar-->
        <!-- START Right Navbar-->
        <ul class="nav navbar-nav navbar-right">
            <!-- Search icon-->
            <!--
            <li>
                <a (click)="openNavSearch($event)"><em class="icon-magnifier"></em></a>
            </li>
            -->
            <!-- START Alert menu-->
            <!--
            <li class="dropdown dropdown-list" dropdown>
                <a dropdownToggle>
                    <em class="icon-bell"></em>
                    <div class="label label-danger">11</div>
                </a>
                <!-- START Dropdown menu- ->
                <ul dropdownMenu class="dropdown-menu animated flipInX">
                    <li>
                        <!-- START list group- ->
                        <div class="list-group">
                            <!-- list item- ->
                            <a class="list-group-item">
                                <div class="media-box">
                                    <div class="pull-left">
                                        <em class="fa fa-twitter fa-2x text-info"></em>
                                    </div>
                                    <div class="media-box-body clearfix">
                                        <p class="m0">New followers</p>
                                        <p class="m0 text-muted"><small>1 new follower</small></p>
                                    </div>
                                </div>
                            </a>
                            <!-- list item- ->
                            <a class="list-group-item">
                                <div class="media-box">
                                    <div class="pull-left">
                                        <em class="fa fa-envelope fa-2x text-warning"></em>
                                    </div>
                                    <div class="media-box-body clearfix">
                                        <p class="m0">New e-mails</p>
                                        <p class="m0 text-muted"><small>You have 10 new emails</small></p>
                                    </div>
                                </div>
                            </a>
                            <!-- list item- ->
                            <a class="list-group-item">
                                <div class="media-box">
                                    <div class="pull-left">
                                        <em class="fa fa-tasks fa-2x text-success"></em>
                                    </div>
                                    <div class="media-box-body clearfix">
                                        <p class="m0">Pending Tasks</p>
                                        <p class="m0 text-muted"><small>11 pending task</small></p>
                                    </div>
                                </div>
                            </a>
                            <!-- last list item- ->
                            <a class="list-group-item">
                                <small translate="topbar.notification.MORE">More notifications</small>
                                <span class="label label-danger pull-right">14</span>
                            </a>
                        </div>
                        <!-- END list group- ->
                    </li>
                </ul>
                <!-- END Dropdown menu- ->
            </li>
            -->
            <!-- END Alert menu-->
        </ul>
        <!-- END Right Navbar-->
    </div>
    <!-- END Nav wrapper-->
    <app-navsearch [visible]="getNavSearchVisible()" (onclose)="setNavSearchVisible(false)"></app-navsearch>
</nav>
<!-- END Top Navbar-->
